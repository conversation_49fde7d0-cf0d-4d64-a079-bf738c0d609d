#!/usr/bin/env python3
"""
测试小波变换分析功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.calculation import WaveletAnalyzer

def test_wavelet_analysis():
    """测试小波变换分析功能"""
    
    # 测试数据 - 使用正弦波信号
    test_data = [
        {
            "name": "Test Signal",
            "data": [
                {"time": "2025-03-16 11:20:00.000", "value": 0.0},
                {"time": "2025-03-16 11:20:01.000", "value": 0.5},
                {"time": "2025-03-16 11:20:02.000", "value": 1.0},
                {"time": "2025-03-16 11:20:03.000", "value": 0.5},
                {"time": "2025-03-16 11:20:04.000", "value": 0.0},
                {"time": "2025-03-16 11:20:05.000", "value": -0.5},
                {"time": "2025-03-16 11:20:06.000", "value": -1.0},
                {"time": "2025-03-16 11:20:07.000", "value": -0.5},
                {"time": "2025-03-16 11:20:08.000", "value": 0.0},
                {"time": "2025-03-16 11:20:09.000", "value": 0.5},
                {"time": "2025-03-16 11:20:10.000", "value": 1.0},
                {"time": "2025-03-16 11:20:11.000", "value": 0.5},
                {"time": "2025-03-16 11:20:12.000", "value": 0.0},
                {"time": "2025-03-16 11:20:13.000", "value": -0.5},
                {"time": "2025-03-16 11:20:14.000", "value": -1.0},
                {"time": "2025-03-16 11:20:15.000", "value": -0.5},
            ]
        }
    ]
    
    print("开始测试小波变换分析...")
    
    try:
        # 创建分析器
        analyzer = WaveletAnalyzer()
        
        # 进行分析
        result = analyzer.analyze_multiple_signals(test_data)
        
        print("分析完成！")
        print(f"输入信号数量: {len(test_data)}")
        print(f"输出结果数量: {len(result)}")
        
        for i, signal in enumerate(result):
            print(f"\n信号 {i+1}: {signal['name']}")
            
            # 检查是否有尺度分组数据
            if 'scale_groups' in signal:
                print(f"  尺度分组数量: {len(signal['scale_groups'])}")
                
                for scale_key, scale_group in signal['scale_groups'].items():
                    print(f"  \n  尺度组: {scale_key}")
                    print(f"    名称: {scale_group['name']}")
                    print(f"    尺度值: {scale_group['scale']}")
                    print(f"    频率: {scale_group['frequency']:.4f} Hz")
                    print(f"    数据点数量: {len(scale_group['data'])}")
                    
                    if scale_group['data']:
                        max_value = max([d['value'] for d in scale_group['data']])
                        print(f"    最大功率: {max_value:.4f}")
                        
                        # 显示前5个数据点
                        print("    前5个数据点:")
                        for j, point in enumerate(scale_group['data'][:5]):
                            print(f"      {j+1}. 时间: {point['time']}, 功率: {point['value']:.4f}, 尺度: {point['scale']:.2f}")
                        
                        # 显示后5个数据点
                        if len(scale_group['data']) > 5:
                            print("    后5个数据点:")
                            for j, point in enumerate(scale_group['data'][-5:]):
                                print(f"      {len(scale_group['data'])-4+j}. 时间: {point['time']}, 功率: {point['value']:.4f}, 尺度: {point['scale']:.2f}")
            else:
                # 兼容旧格式
                print(f"  数据点数量: {len(signal['data'])}")
                if signal['data']:
                    max_value = max([d['value'] for d in signal['data']])
                    print(f"  最大功率: {max_value:.4f}")
        
        print("\n测试成功！小波变换分析功能正常工作。")
        print("✓ 尺度分组数据结构")
        print("✓ 每个尺度独立的数据系列")
        print("✓ 功率谱计算")
        print("✓ 数据格式保持一致性")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_wavelet_analysis() 