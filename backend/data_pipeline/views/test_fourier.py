#!/usr/bin/env python3
"""
测试傅立叶分析功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.calculation import FourierAnalyzer

def test_fourier_analysis():
    """测试傅立叶分析功能"""
    
    # 测试数据 - 使用更多的数据点来获得更好的频率分辨率
    test_data = [
        {
            "name": "RIO-logical | RIO L1A | PWM 1 input",
            "data": [
                {"time": "2025-03-16 11:20:00.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:01.000", "value": 0},
                {"time": "2025-03-16 11:20:02.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:03.000", "value": 0},
                {"time": "2025-03-16 11:20:04.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:05.000", "value": 0},
                {"time": "2025-03-16 11:20:06.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:07.000", "value": 0},
                {"time": "2025-03-16 11:20:08.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:09.000", "value": 0},
                {"time": "2025-03-16 11:20:10.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:11.000", "value": 0},
                {"time": "2025-03-16 11:20:12.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:13.000", "value": 0},
                {"time": "2025-03-16 11:20:14.000", "value": 75.2941176470592},
                {"time": "2025-03-16 11:20:15.000", "value": 0},
            ]
        }
    ]
    
    print("开始测试傅立叶分析...")
    
    try:
        # 创建分析器
        analyzer = FourierAnalyzer()
        
        # 进行分析
        result = analyzer.analyze_multiple_signals(test_data)
        
        print("分析完成！")
        print(f"输入信号数量: {len(test_data)}")
        print(f"输出结果数量: {len(result)}")
        
        for i, signal in enumerate(result):
            print(f"\n信号 {i+1}: {signal['name']}")
            print(f"  数据点数量: {len(signal['data'])}")
            if signal['data']:
                print(f"  频率范围: {signal['data'][0]['time']} - {signal['data'][-1]['time']} Hz")
                max_value = max([d['value'] for d in signal['data']])
                print(f"  最大幅度: {max_value:.4f}")
                
                # 分析频率分布
                freqs = [float(d['time']) for d in signal['data']]
                neg_freqs = [f for f in freqs if f < 0]
                zero_freqs = [f for f in freqs if f == 0]
                pos_freqs = [f for f in freqs if f > 0]
                
                print(f"  负频率点数: {len(neg_freqs)}")
                print(f"  零频率点数: {len(zero_freqs)}")
                print(f"  正频率点数: {len(pos_freqs)}")
                
                if neg_freqs:
                    print(f"  负频率范围: {min(neg_freqs):.4f} - {max(neg_freqs):.4f} Hz")
                if pos_freqs:
                    print(f"  正频率范围: {min(pos_freqs):.4f} - {max(pos_freqs):.4f} Hz")
                
                # 显示前10个数据点（包括负频率）
                print("  前10个数据点:")
                for j, point in enumerate(signal['data'][:10]):
                    freq = float(point['time'])
                    phase = float(point['phase']) if 'phase' in point else 0.0
                    print(f"    {j+1}. 频率: {freq:8.4f} Hz, 幅度: {point['value']:8.4f}, 相位: {phase:8.4f} rad")
                
                # 显示后10个数据点（包括正频率）
                if len(signal['data']) > 10:
                    print("  后10个数据点:")
                    for j, point in enumerate(signal['data'][-10:]):
                        freq = float(point['time'])
                        phase = float(point['phase']) if 'phase' in point else 0.0
                        print(f"    {len(signal['data'])-9+j}. 频率: {freq:8.4f} Hz, 幅度: {point['value']:8.4f}, 相位: {phase:8.4f} rad")
        
        print("\n测试成功！傅立叶分析功能正常工作。")
        print("✓ 完整频率谱计算（包括正负频率）")
        print("✓ 相位信息计算")
        print("✓ 数据格式保持一致性")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_fourier_analysis() 