FROM registry.cn-zhangjiakou.aliyuncs.com/dvadmin-pro/dvadmin3-base-backend:latest
WORKDIR /backend
# 删除 COPY 指令，由 docker-compose 挂载代码
COPY backend/conf/ /backend/conf/
RUN python3 -m pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements.txt
# 确保 celery 可执行文件在 PATH 中
RUN which python3 && python3 -c "import celery; print(celery.__version__)"
CMD ["/usr/local/bin/python3", "-m", "celery", "-A", "application", "worker", "-B", "--loglevel=info"]